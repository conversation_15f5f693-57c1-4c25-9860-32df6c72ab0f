export type TypeMerge<T> = {
    [K in keyof T]: T[K];
} & {};

export type TypePartial<T> = T extends object
    ? {
          [P in keyof T]?: TypePartial<T[P]>;
      }
    : T;

export type TypePrimitive =
    | string
    | number
    | boolean
    | bigint
    | symbol
    | null
    | undefined;

export type TypeReturn<T extends (...args: any) => any, NewReturn> = (
    ...args: Parameters<T>
) => NewReturn;

export type TypeStripThis<T> = {
    [K in keyof T]: T[K] extends (this: any, ...a: infer A) => infer R
        ? (...a: A) => R
        : T[K];
};
