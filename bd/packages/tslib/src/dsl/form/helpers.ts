// import type { DSLRegistry } from "@clara/dsl/spec/registry";
// import type * as Spec from "@clara/dsl/spec/req";

// export interface DSLForm extends Spec.DSLReq {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModel extends Spec.DSLReqFieldModel {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModelAccess extends Spec.DSLReqFieldModelAccess {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModelDynamic extends Spec.DSLReqFieldModelDynamic {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModelIfDefaults
//     extends Spec.DSLReqFieldModelIfDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModelSourceFilterDefaults
//     extends Spec.DSLReqFieldModelSourceFilterDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModelSubfieldsDefaults
//     extends Spec.DSLReqFieldModelSubfieldsDefaults {
//     isVisible(): boolean;
// }

// export interface DSLFormFields extends Spec.DSLReqFields {}

// export interface DSLFormFieldsDefaults extends Spec.DSLReqFieldsDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldView extends Spec.DSLReqFieldView {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldViewEmbed extends Spec.DSLReqFieldViewEmbed {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldViewGrid extends Spec.DSLReqFieldViewGrid {
//     isVisible(): boolean;
// }
// export interface DSLFormModel extends Spec.DSLReqModel {
//     isVisible(): boolean;
// }
// export interface DSLFormModelAccess extends Spec.DSLReqModelAccess {
//     isVisible(): boolean;
// }
// export interface DSLFormModelPrefillDefaults
//     extends Spec.DSLReqModelPrefillDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormModelSectionsDefaults
//     extends Spec.DSLReqModelSectionsDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormView extends Spec.DSLReqView {
//     isVisible(): boolean;
// }
// export interface DSLFormViewBlock extends Spec.DSLReqViewBlock {
//     isVisible(): boolean;
// }
// export interface DSLFormViewGrid extends Spec.DSLReqViewGrid {
//     isVisible(): boolean;
// }
// export interface DSLFormViewGridAggregateDefaults
//     extends Spec.DSLReqViewGridAggregateDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormViewGridStyleDefaults
//     extends Spec.DSLReqViewGridStyleDefaults {
//     isVisible(): boolean;
// }

// prettier-ignore
// type FormOf<T> =
//     // ------- one-to-one Req ⇢ Form pairs -----------
//     T extends Spec.DSLReq                             ? DSLForm :
//     T extends Spec.DSLReqFieldModel                   ? DSLFormFieldModel :
//     T extends Spec.DSLReqFieldModelAccess             ? DSLFormFieldModelAccess :
//     T extends Spec.DSLReqFieldModelDynamic            ? DSLFormFieldModelDynamic :
//     T extends Spec.DSLReqFieldModelIfDefaults         ? DSLFormFieldModelIfDefaults :
//     T extends Spec.DSLReqFieldModelSourceFilterDefaults ? DSLFormFieldModelSourceFilterDefaults :
//     T extends Spec.DSLReqFieldModelSubfieldsDefaults  ? DSLFormFieldModelSubfieldsDefaults :
//     T extends Spec.DSLReqFields                       ? DSLFormFields :
//     T extends Spec.DSLReqFieldsDefaults               ? DSLFormFieldsDefaults :
//     T extends Spec.DSLReqFieldView                    ? DSLFormFieldView :
//     T extends Spec.DSLReqFieldViewEmbed               ? DSLFormFieldViewEmbed :
//     T extends Spec.DSLReqFieldViewGrid                ? DSLFormFieldViewGrid :
//     T extends Spec.DSLReqModel                        ? DSLFormModel :
//     T extends Spec.DSLReqModelAccess                  ? DSLFormModelAccess :
//     T extends Spec.DSLReqModelPrefillDefaults         ? DSLFormModelPrefillDefaults :
//     T extends Spec.DSLReqModelSectionsDefaults        ? DSLFormModelSectionsDefaults :
//     T extends Spec.DSLReqView                         ? DSLFormView :
//     T extends Spec.DSLReqViewBlock                    ? DSLFormViewBlock :
//     T extends Spec.DSLReqViewGrid                     ? DSLFormViewGrid :
//     T extends Spec.DSLReqViewGridAggregateDefaults    ? DSLFormViewGridAggregateDefaults :
//     T extends Spec.DSLReqViewGridStyleDefaults        ? DSLFormViewGridStyleDefaults :

//     // -------- recurse through all other objects ----
//     T extends object ? { [K in keyof T]: FormOf<T[K]> } :

//     // ------------------------------------------------
//     never;

// export const toForm = <R extends Spec.DSLReq>(req: R): FormOf<R> =>
//     req as unknown as FormOf<R>; // compile-time transformation only

// 1. one shared symbol (completely invisible in IntelliSense)
// declare const __type: unique symbol;
// type __type = typeof __type;

// DSLReq base interface for branding
// interface DSL<T> {
//     readonly [__type]?: T;
// }

// type ToForm<T> =
//     T extends DSL<infer K>
//         ? K extends keyof DSLRegistry
//             ? DSLRegistry[K]
//             : never
//         : T extends readonly (infer R)[]
//           ? readonly ToForm<R>[]
//           : T extends object
//             ? { [P in keyof T]: ToForm<T[P]> }
//             : T;

// export function toForm<T>(req: T): ToForm<T> {
//     const walk = (v: any): any => {
//         if (Array.isArray(v)) return v.map(walk);
//         if (v && typeof v === "object") {
//             // if the object has its own converter expose it, otherwise dive
//             const out: any = {};
//             for (const [k, val] of Object.entries(v)) out[k] = walk(val);
//             return out;
//         }
//         return v;
//     };
//     return walk(req) satisfies ToForm<T>;
// }

// 4. brand that carries the key -------------------------------------------
// type Brand<K extends keyof DSLRegistry> = { readonly [__type]?: K };

// 5. Automatic type mapping with branding
// This approach uses TypeScript's declaration merging to extend DSL interfaces

// Apply branding to all DSLReq interfaces automatically
// declare module "@clara/dsl/spec/req" {
//     // Apply branding to each interface type
//     export interface DSLReqFieldModel extends Brand<"DSLReqFieldModel"> {}
//     export interface DSLReqFieldModelAccess
//         extends Brand<"DSLReqFieldModelAccess"> {}
//     export interface DSLReqFieldModelDynamic
//         extends Brand<"DSLReqFieldModelDynamic"> {}
//     export interface DSLReqFieldModelIfDefaults
//         extends Brand<"DSLReqFieldModelIfDefaults"> {}
//     // ... and so on for other interfaces
// }

// 6. helper picker ---------------------------------------------------------
// export type HelpersFor<T> = T extends { readonly [__type]?: infer K }
//     ? K extends keyof DSLRegistry
//         ? DSLRegistry[K]
//         : {}
//     : {};

// 7. deep mapper (unchanged) ----------------------------------------------
// export type DeepHelpers<T> = T extends (...a: any) => any
//     ? T
//     : T extends readonly (infer U)[]
//       ? readonly DeepHelpers<U>[]
//       : T extends object
//         ? { [P in keyof T]: DeepHelpers<T[P]> } & HelpersFor<T>
//         : T;

// ---------------------------------------------------------------------------
// 3.  ─── RUNTIME WRAPPER  (one Proxy, memoised) ─────────────────────────────
// const proxyCache = new WeakMap<object>();

// export function withHelpers<T extends object>(obj: T): DeepHelpers<T> {
//     if (proxyCache.has(obj)) return proxyCache.get(obj);

//     const proxy = new Proxy(obj as any, {
//         get(target, prop, receiver) {
//             // /* -------------------- inject helper implementations ----------------- */
//             // if (prop === "isVisible" && isSection(target)) {
//             //     return () => !target.hide_header; // <– replace w/ real rule
//             // }
//             // if (prop === "isRequired" && isField(target)) {
//             //     return () => target.required; // <– example
//             // }
//             // /* ------------------------------------------------------------------- */

//             const value = Reflect.get(target, prop, receiver);
//             // recurse only for plain objects/arrays
//             if (value && typeof value === "object") {
//                 return withHelpers(value);
//             }
//             return value;
//         },
//     });

//     proxyCache.set(obj, proxy);
//     return proxy;
// }

// simple structural tests ─ tune at will
// const isSection = (x: any): x is DSLReqModelSectionsDefaults =>
//     x && typeof x === "object" && "tab" in x && "hide_header" in x;

// const isField = (x: any): x is DSLReqFieldModel =>
//     x && typeof x === "object" && "required" in x && "type" in x;

// export interface DSLForm
//     extends DeepHelpers<Spec.DSLReq>,
//         TypeStripThis<typeof DSLForm> {}
// export interface DSLFormFieldModel
//     extends DeepHelpers<Spec.DSLReqFieldModel>,
//         TypeStripThis<typeof DSLFormFieldModel> {}
// export interface DSLFormFieldModelAccess
//     extends DeepHelpers<Spec.DSLReqFieldModelAccess>,
//         TypeStripThis<typeof DSLFormFieldModelAccess> {}
// export interface DSLFormFieldModelDynamic
//     extends DeepHelpers<Spec.DSLReqFieldModelDynamic>,
//         TypeStripThis<typeof DSLFormFieldModelDynamic> {}
// export interface DSLFormFieldModelIfDefaults
//     extends DeepHelpers<Spec.DSLReqFieldModelIfDefaults>,
//         TypeStripThis<typeof DSLFormFieldModelIfDefaults> {}
// export interface DSLFormFieldModelSourceFilterDefaults
//     extends DeepHelpers<Spec.DSLReqFieldModelSourceFilterDefaults>,
//         TypeStripThis<typeof DSLFormFieldModelSourceFilterDefaults> {}
// export interface DSLFormFieldModelSubfieldsDefaults
//     extends DeepHelpers<Spec.DSLReqFieldModelSubfieldsDefaults>,
//         TypeStripThis<typeof DSLFormFieldModelSubfieldsDefaults> {}
// export interface DSLFormFieldsDefaults
//     extends DeepHelpers<Spec.DSLReqFieldsDefaults>,
//         TypeStripThis<typeof DSLFormFieldsDefaults> {}
// export interface DSLFormFieldView
//     extends DeepHelpers<Spec.DSLReqFieldView>,
//         TypeStripThis<typeof DSLFormFieldView> {}
// export interface DSLFormFieldViewEmbed
//     extends DeepHelpers<Spec.DSLReqFieldViewEmbed>,
//         TypeStripThis<typeof DSLFormFieldViewEmbed> {}
// export interface DSLFormFieldViewGrid
//     extends DeepHelpers<Spec.DSLReqFieldViewGrid>,
//         TypeStripThis<typeof DSLFormFieldViewGrid> {}
// export interface DSLFormModel
//     extends DeepHelpers<Spec.DSLReqModel>,
//         TypeStripThis<typeof DSLFormModel> {}
// export interface DSLFormModelAccess
//     extends DeepHelpers<Spec.DSLReqModelAccess>,
//         TypeStripThis<typeof DSLFormModelAccess> {}
// export interface DSLFormModelPrefillDefaults
//     extends DeepHelpers<Spec.DSLReqModelPrefillDefaults>,
//         TypeStripThis<typeof DSLFormModelPrefillDefaults> {}
// export interface DSLFormModelSectionsDefaults
//     extends DeepHelpers<Spec.DSLReqModelSectionsDefaults>,
//         TypeStripThis<typeof DSLFormModelSectionsDefaults> {}
// export interface DSLFormView
//     extends DeepHelpers<Spec.DSLReqView>,
//         TypeStripThis<typeof DSLFormView> {}
// export interface DSLFormViewBlock
//     extends DeepHelpers<Spec.DSLReqViewBlock>,
//         TypeStripThis<typeof DSLFormViewBlock> {}
// export interface DSLFormViewGrid
//     extends DeepHelpers<Spec.DSLReqViewGrid>,
//         TypeStripThis<typeof DSLFormViewGrid> {}
// export interface DSLFormViewGridAggregateDefaults
//     extends DeepHelpers<Spec.DSLReqViewGridAggregateDefaults>,
//         TypeStripThis<typeof DSLFormViewGridAggregateDefaults> {}
// export interface DSLFormViewGridStyleDefaults
//     extends DeepHelpers<Spec.DSLReqViewGridStyleDefaults>,
//         TypeStripThis<typeof DSLFormViewGridStyleDefaults> {}

// export function dslFormx<T extends Spec.DSLReq>(data: T): DeepHelpers<T> {
//   return attach(data) as DeepHelpers<T>;

//   /* ----- runtime helper injector (very small & cheap) ---- */
//   function attach(o: any): any {
//     if (o === null || typeof o !== "object") return o;

//     // pick helper by “shape” (fast, no expensive RTTI)
//     const helper: object =
//       "fields" in o && "model" in o && "view" in o         ? DSLMethod
//     : "fields" in o && !("model" in o)                     ? DSLMethodFields
//     : "model"  in o && "view" in o && "access" in o        ? DSLMethodFieldsDefaults
//     : "access" in o && "type" in o                         ? DSLMethodFieldModel
//     : "request_type" in o && "embed" in o ? DSLMethodFieldView
//     : {};   // fallback

//     const proxy = Object.assign(Object.create(helper), o);
//     for (const k in proxy) proxy[k] = attach(proxy[k]);
//     return proxy;
//   }
// }

// export const DSLForm = makeDSL<"Form">({});

// // export type DSLForm = Spec.DSLReq & typeof DSLForm;

// export const DSLFormFields = makeDSL<"FormFields">(M.DSLMethodFields);
// export const DSLFormFieldModel = makeDSL<"FormFieldModel">(
//     M.DSLMethodFieldModel
// );
// export const DSLFormFieldModelAccess = makeDSL<"FormFieldModelAccess">(
//     M.DSLMethodFieldModelAccess
// );
// export const DSLFormFieldModelDynamic = makeDSL<"FormFieldModelDynamic">(
//     M.DSLMethodFieldModelDynamic
// );
// export const DSLFormFieldModelIfDefaults = makeDSL<"FormFieldModelIfDefaults">(
//     M.DSLMethodFieldModelIfDefaults
// );
// export const DSLFormFieldModelSourceFilterDefaults =
//     makeDSL<"FormFieldModelSourceFilterDefaults">(
//         M.DSLMethodFieldModelSourceFilterDefaults
//     );
// export const DSLFormFieldModelSubfieldsDefaults =
//     makeDSL<"FormFieldModelSubfieldsDefaults">(
//         M.DSLMethodFieldModelSubfieldsDefaults
//     );
// export const DSLFormFieldsDefaults = makeDSL<"FormFieldsDefaults">(
//     M.DSLMethodFieldsDefaults
// );
// export const DSLFormFieldView = makeDSL<"FormFieldView">(M.DSLMethodFieldView);
// export const DSLFormFieldViewEmbed = makeDSL<"FormFieldViewEmbed">(
//     M.DSLMethodFieldViewEmbed
// );
// export const DSLFormFieldViewGrid = makeDSL<"FormFieldViewGrid">(
//     M.DSLMethodFieldViewGrid
// );
// export const DSLFormModel = makeDSL<"FormModel">(M.DSLMethodModel);
// export const DSLFormModelAccess = makeDSL<"FormModelAccess">(
//     M.DSLMethodModelAccess
// );
// export const DSLFormModelPrefillDefaults = makeDSL<"FormModelPrefillDefaults">(
//     M.DSLMethodModelPrefillDefaults
// );
// export const DSLFormModelSectionsDefaults =
//     makeDSL<"FormModelSectionsDefaults">(M.DSLMethodModelSectionsDefaults);
// export const DSLFormView = makeDSL<"FormView">(M.DSLMethodView);
// export const DSLFormViewBlock = makeDSL<"FormViewBlock">(M.DSLMethodViewBlock);
// export const DSLFormViewGrid = makeDSL<"FormViewGrid">(M.DSLMethodViewGrid);
// export const DSLFormViewGridAggregateDefaults =
//     makeDSL<"FormViewGridAggregateDefaults">(
//         M.DSLMethodViewGridAggregateDefaults
//     );
// export const DSLFormViewGridStyleDefaults =
//     makeDSL<"FormViewGridStyleDefaults">(M.DSLMethodViewGridStyleDefaults);

// type SpecMap = {
//     Form: Spec.DSLReq;
//     FormFieldModel: Spec.DSLReqFieldModel;
//     FormFieldModelAccess: Spec.DSLReqFieldModelAccess;
//     FormFieldModelDynamic: Spec.DSLReqFieldModelDynamic;
//     FormFieldModelIfDefaults: Spec.DSLReqFieldModelIfDefaults;
//     FormFieldModelSourceFilterDefaults: Spec.DSLReqFieldModelSourceFilterDefaults;
//     FormFieldModelSubfieldsDefaults: Spec.DSLReqFieldModelSubfieldsDefaults;
//     FormFields: Spec.DSLReqFields;
//     FormFieldsDefaults: Spec.DSLReqFieldsDefaults;
//     FormFieldView: Spec.DSLReqFieldView;
//     FormFieldViewEmbed: Spec.DSLReqFieldViewEmbed;
//     FormFieldViewGrid: Spec.DSLReqFieldViewGrid;
//     FormModel: Spec.DSLReqModel;
//     FormModelAccess: Spec.DSLReqModelAccess;
//     FormModelPrefillDefaults: Spec.DSLReqModelPrefillDefaults;
//     FormModelSectionsDefaults: Spec.DSLReqModelSectionsDefaults;
//     FormView: Spec.DSLReqView;
//     FormViewBlock: Spec.DSLReqViewBlock;
//     FormViewGrid: Spec.DSLReqViewGrid;
//     FormViewGridAggregateDefaults: Spec.DSLReqViewGridAggregateDefaults;
//     FormViewGridStyleDefaults: Spec.DSLReqViewGridStyleDefaults;
// };

// type DSL<K extends keyof SpecMap> = DeepHelpers<SpecMap[K]>;
// type M<K extends keyof SpecMap> = {
//     [P in string]: (this: DSL<K>, ...a: any[]) => any;
// };

// function makeDSL<K extends keyof SpecMap>(obj: M<K>): M<K> & DSL<K> {
//     return obj as M<K> & DSL<K>;
// }

//export const dslForm = makeDSL;

// export const DSLFormFields = makeDSL<"FormFields">({
//     isVisible(): boolean {
//         /* ... */
//         return true;
//     },
// });

// export const DSLFormFieldModel = makeDSL<"FormFieldModel">({
//     isVisible(): boolean {
//         /* ... */
//         return true;
//     },
// });

// export const DSLFormFieldModelAccess = makeDSL<"FormFieldModelAccess">({
//     hasAccess(): boolean {
//         /* ... */
//         return false;
//     },
// });

// export const DSLFormFieldModelDynamic = makeDSL<"FormFieldModelDynamic">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormFieldModelIfDefaults = makeDSL<"FormFieldModelIfDefaults">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormFieldModelSourceFilterDefaults =
//     makeDSL<"FormFieldModelSourceFilterDefaults">({
//         getPath() {
//             /* ... */
//             return "...";
//         },
//     });

// export const DSLFormFieldModelSubfieldsDefaults =
//     makeDSL<"FormFieldModelSubfieldsDefaults">({
//         getPath() {
//             /* ... */
//             return "...";
//         },
//     });

// export const DSLFormFieldsDefaults = makeDSL<"FormFieldsDefaults">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormFieldView = makeDSL<"FormFieldView">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormFieldViewEmbed = makeDSL<"FormFieldView">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormFieldViewGrid = makeDSL<"FormFieldView">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormModel = makeDSL<"FormModel">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormModelAccess = makeDSL<"FormModelAccess">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormModelPrefillDefaults = makeDSL<"FormModelPrefillDefaults">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormModelSectionsDefaults =
//     makeDSL<"FormModelSectionsDefaults">({
//         getPath() {
//             /* ... */
//             return "...";
//         },
//     });

// export const DSLFormView = makeDSL<"FormView">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormViewBlock = makeDSL<"FormViewBlock">({
//     getPath() {
//         /* ... */
//         return "...";
//     },
// });

// export const DSLFormViewGrid = makeDSL<"FormViewGrid">({
//     setFilters(_filters: Record<string, unknown>) {
//         /* ... */
//         return;
//     },
// });

// export const DSLFormViewGridAggregateDefaults =
//     makeDSL<"FormViewGridAggregateDefaults">({
//         getPath() {
//             /* ... */
//             return "...";
//         },
//     });

// export const DSLFormViewGridStyleDefaults =
//     makeDSL<"FormViewGridStyleDefaults">({
//         getGridStyle() {
//             /* ... */
//             return "...";
//         },
//     });

// export interface DSLForm extends Spec.DSLReq {
//     load(): void;
// }
// export interface DSLFormFieldModel extends Spec.DSLReqFieldModel {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModelAccess extends Spec.DSLReqFieldModelAccess {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModelDynamic extends Spec.DSLReqFieldModelDynamic {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModelIfDefaults
//     extends Spec.DSLReqFieldModelIfDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModelSourceFilterDefaults
//     extends Spec.DSLReqFieldModelSourceFilterDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldModelSubfieldsDefaults
//     extends Spec.DSLReqFieldModelSubfieldsDefaults {
//     isVisible(): boolean;
// }

// export interface DSLFormFields extends Spec.DSLReqFields {}

// export interface DSLFormFieldsDefaults extends Spec.DSLReqFieldsDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldView extends Spec.DSLReqFieldView {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldViewEmbed extends Spec.DSLReqFieldViewEmbed {
//     isVisible(): boolean;
// }
// export interface DSLFormFieldViewGrid extends Spec.DSLReqFieldViewGrid {
//     isVisible(): boolean;
// }
// export interface DSLFormModel extends Spec.DSLReqModel {
//     isVisible(): boolean;
// }
// export interface DSLFormModelAccess extends Spec.DSLReqModelAccess {
//     isVisible(): boolean;
// }
// export interface DSLFormModelPrefillDefaults
//     extends Spec.DSLReqModelPrefillDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormModelSectionsDefaults
//     extends Spec.DSLReqModelSectionsDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormView extends Spec.DSLReqView {
//     getLabel(): boolean;
// }
// export interface DSLFormViewBlock extends Spec.DSLReqViewBlock {
//     isVisible(): boolean;
// }
// export interface DSLFormViewGrid extends Spec.DSLReqViewGrid {
//     isVisible(): boolean;
// }
// export interface DSLFormViewGridAggregateDefaults
//     extends Spec.DSLReqViewGridAggregateDefaults {
//     isVisible(): boolean;
// }
// export interface DSLFormViewGridStyleDefaults
//     extends Spec.DSLReqViewGridStyleDefaults {
//     isVisible(): boolean;
// }

// prettier-ignore
// type ReqFormPairs =
// | [Spec.DSLReq, typeof DSLForm]
// | [Spec.DSLReqFieldModel, typeof DSLFormFieldModel]
// | [Spec.DSLReqFieldModelAccess, typeof DSLFormFieldModelAccess]
// | [Spec.DSLReqFieldModelDynamic, typeof DSLFormFieldModelDynamic]
// | [Spec.DSLReqFieldModelIfDefaults, typeof DSLFormFieldModelIfDefaults]
// | [Spec.DSLReqFieldModelSourceFilterDefaults, typeof DSLFormFieldModelSourceFilterDefaults]
// | [Spec.DSLReqFieldModelSubfieldsDefaults, typeof DSLFormFieldModelSubfieldsDefaults]
// | [Spec.DSLReqFields, typeof DSLFormFields]
// | [Spec.DSLReqFieldsDefaults, typeof DSLFormFieldsDefaults]
// | [Spec.DSLReqFieldView, typeof DSLFormFieldView]
// | [Spec.DSLReqFieldViewEmbed, typeof DSLFormFieldViewEmbed]
// | [Spec.DSLReqFieldViewGrid, typeof DSLFormFieldViewGrid]
// | [Spec.DSLReqModel, typeof DSLFormModel]
// | [Spec.DSLReqModelAccess, typeof DSLFormModelAccess]
// | [Spec.DSLReqModelPrefillDefaults, typeof DSLFormModelPrefillDefaults]
// | [Spec.DSLReqModelSectionsDefaults, typeof DSLFormModelSectionsDefaults]
// | [Spec.DSLReqView, typeof DSLFormView]
// | [Spec.DSLReqViewBlock, typeof DSLFormViewBlock]
// | [Spec.DSLReqViewGrid, typeof DSLFormViewGrid]
// | [Spec.DSLReqViewGridAggregateDefaults, typeof DSLFormViewGridAggregateDefaults]
// | [Spec.DSLReqViewGridStyleDefaults, typeof DSLFormViewGridStyleDefaults];

// type MapReq<T> =
//     Extract<ReqFormPairs, [T, any]> extends [T, infer F] ? F : never;

// type Formify<T> =
//     // primitives
//     T extends string | number | boolean | null | undefined
//         ? T
//         : // functions stay functions
//           T extends (...args: any[]) => any
//           ? T
//           : // arrays / tuples
//             T extends readonly (infer U)[]
//             ? ReadonlyArray<Formify<U>>
//             : // objects → original shape ∧ Form mix-in ∧ deep children
//               T extends object
//               ? (T & (MapReq<T> extends never ? {} : MapReq<T>)) & {
//                     [K in keyof T]: Formify<T[K]>;
//                 }
//               : T;

// export const dslForm = <R extends Spec.DSLReq>(req: R) =>
//     req as unknown as Formify<R>;

// type FormOf<T> =
//     // ------- one-to-one Req ⇢ Form pairs -----------
//     T extends Spec.DSLReq                             ? DSLForm :
//     T extends Spec.DSLReqFieldModel                   ? DSLFormFieldModel :
//     T extends Spec.DSLReqFieldModelAccess             ? DSLFormFieldModelAccess :
//     T extends Spec.DSLReqFieldModelDynamic            ? DSLFormFieldModelDynamic :
//     T extends Spec.DSLReqFieldModelIfDefaults         ? DSLFormFieldModelIfDefaults :
//     T extends Spec.DSLReqFieldModelSourceFilterDefaults ? DSLFormFieldModelSourceFilterDefaults :
//     T extends Spec.DSLReqFieldModelSubfieldsDefaults  ? DSLFormFieldModelSubfieldsDefaults :
//     T extends Spec.DSLReqFields                       ? DSLFormFields :
//     T extends Spec.DSLReqFieldsDefaults               ? DSLFormFieldsDefaults :
//     T extends Spec.DSLReqFieldView                    ? DSLFormFieldView :
//     T extends Spec.DSLReqFieldViewEmbed               ? DSLFormFieldViewEmbed :
//     T extends Spec.DSLReqFieldViewGrid                ? DSLFormFieldViewGrid :
//     T extends Spec.DSLReqModel                        ? DSLFormModel :
//     T extends Spec.DSLReqModelAccess                  ? DSLFormModelAccess :
//     T extends Spec.DSLReqModelPrefillDefaults         ? DSLFormModelPrefillDefaults :
//     T extends Spec.DSLReqModelSectionsDefaults        ? DSLFormModelSectionsDefaults :
//     T extends Spec.DSLReqView                         ? DSLFormView :
//     T extends Spec.DSLReqViewBlock                    ? DSLFormViewBlock :
//     T extends Spec.DSLReqViewGrid                     ? DSLFormViewGrid :
//     T extends Spec.DSLReqViewGridAggregateDefaults    ? DSLFormViewGridAggregateDefaults :
//     T extends Spec.DSLReqViewGridStyleDefaults        ? DSLFormViewGridStyleDefaults :

//     // -------- recurse through all other objects ----
//     T extends object ? { [K in keyof T]: FormOf<T[K]> } :

//     // ------------------------------------------------
//     never;

// export const dslForm = <R extends Spec.DSLReq>(req: R): FormOf<R> =>
//     req as unknown as FormOf<R>; // compile-time transformation only

// import type { DSLSpecRequired } from "@clara/dsl";
// import { TypeStripThis } from "../../type/generics.ts";
// import { withHelpers, DeepHelpers } from "./helpers.ts";

/** Final public type */
// export interface DSLForm
//     extends DeepHelpers<DSLSpecRequired>,
//         TypeStripThis<typeof DSLFormMethods> {}

// /** Your normal method bag */
// export const DSLFormMethods = {
//     load(this: DSLForm, id: string | number) {
//         /* ... */
//         return String(id);
//     },
// } as const;

// export function dslFormx(raw: DSLSpecRequired): DSLForm {
//     // one call – whole tree gets wrapped
//     return withHelpers({
//         ...raw,
//         ...DSLFormMethods,
//     });
// }

// import * as M from "./methods.ts";

// type DSLMethod = typeof M.DSLMethod;
// type DSLMethodFields = typeof M.DSLMethodFields;
// type DSLMethodFieldModel = typeof M.DSLMethodFieldModel;
// type DSLMethodFieldModelAccess = typeof M.DSLMethodFieldModelAccess;
// type DSLMethodFieldModelDynamic = typeof M.DSLMethodFieldModelDynamic;
// type DSLMethodFieldModelIfDefaults = typeof M.DSLMethodFieldModelIfDefaults;
// type DSLMethodFieldModelSourceFilterDefaults =
//     typeof M.DSLMethodFieldModelSourceFilterDefaults;
// type DSLMethodFieldModelSubfieldsDefaults =
//     typeof M.DSLMethodFieldModelSubfieldsDefaults;
// type DSLMethodFieldsDefaults = typeof M.DSLMethodFieldsDefaults;
// type DSLMethodFieldView = typeof M.DSLMethodFieldView;
// type DSLMethodFieldViewEmbed = typeof M.DSLMethodFieldViewEmbed;
// type DSLMethodFieldViewGrid = typeof M.DSLMethodFieldViewGrid;
// type DSLMethodModel = typeof M.DSLMethodModel;
// type DSLMethodModelAccess = typeof M.DSLMethodModelAccess;
// type DSLMethodModelPrefillDefaults = typeof M.DSLMethodModelPrefillDefaults;
// type DSLMethodModelSectionsDefaults = typeof M.DSLMethodModelSectionsDefaults;
// type DSLMethodView = typeof M.DSLMethodView;
// type DSLMethodViewBlock = typeof M.DSLMethodViewBlock;
// type DSLMethodViewGrid = typeof M.DSLMethodViewGrid;
// type DSLMethodViewGridAggregateDefaults =
//     typeof M.DSLMethodViewGridAggregateDefaults;
// type DSLMethodViewGridStyleDefaults = typeof M.DSLMethodViewGridStyleDefaults;

// export interface DSLForm extends Spec.DSLReq, DSLMethod {}
// export interface DSLFormFieldModel
//     extends Spec.DSLReqFieldModel,
//         DSLMethodFieldModel {}
// export interface DSLFormFieldModelAccess
//     extends Spec.DSLReqFieldModelAccess,
//         DSLMethodFieldModelAccess {}
// export interface DSLFormFieldModelDynamic
//     extends Spec.DSLReqFieldModelDynamic,
//         DSLMethodFieldModelDynamic {}
// export interface DSLFormFieldModelIfDefaults
//     extends Spec.DSLReqFieldModelIfDefaults,
//         DSLMethodFieldModelIfDefaults {}
// export interface DSLFormFieldModelSourceFilterDefaults
//     extends Spec.DSLReqFieldModelSourceFilterDefaults,
//         DSLMethodFieldModelSourceFilterDefaults {}
// export interface DSLFormFieldModelSubfieldsDefaults
//     extends Spec.DSLReqFieldModelSubfieldsDefaults,
//         DSLMethodFieldModelSubfieldsDefaults {}
// export interface DSLFormFields extends Spec.DSLReqFields, DSLMethodFields {}
// export interface DSLFormFieldsDefaults
//     extends Spec.DSLReqFieldsDefaults,
//         DSLMethodFieldsDefaults {}
// export interface DSLFormFieldView
//     extends Spec.DSLReqFieldView,
//         DSLMethodFieldView {}
// export interface DSLFormFieldViewEmbed
//     extends Spec.DSLReqFieldViewEmbed,
//         DSLMethodFieldViewEmbed {}
// export interface DSLFormFieldViewGrid
//     extends Spec.DSLReqFieldViewGrid,
//         DSLMethodFieldViewGrid {}
// export interface DSLFormModel extends Spec.DSLReqModel, DSLMethodModel {}
// export interface DSLFormModelAccess
//     extends Spec.DSLReqModelAccess,
//         DSLMethodModelAccess {}
// export interface DSLFormModelPrefillDefaults
//     extends Spec.DSLReqModelPrefillDefaults,
//         DSLMethodModelPrefillDefaults {}
// export interface DSLFormModelSectionsDefaults
//     extends Spec.DSLReqModelSectionsDefaults,
//         DSLMethodModelSectionsDefaults {}
// export interface DSLFormView extends Spec.DSLReqView, DSLMethodView {}
// export interface DSLFormViewBlock
//     extends Spec.DSLReqViewBlock,
//         DSLMethodViewBlock {}
// export interface DSLFormViewGrid
//     extends Spec.DSLReqViewGrid,
//         DSLMethodViewGrid {}
// export interface DSLFormViewGridAggregateDefaults
//     extends Spec.DSLReqViewGridAggregateDefaults,
//         DSLMethodViewGridAggregateDefaults {}
// export interface DSLFormViewGridStyleDefaults
//     extends Spec.DSLReqViewGridStyleDefaults,
//         DSLMethodViewGridStyleDefaults {}

// const DSLMethod = {
//     load(id: string | number): string {
//         // ‘this’ is DSL<'Form'>
//         return String(id);
//     },
//     rows(): DSLForm[] | null {
//         return [dslForm(this)] as DSLForm[];
//     },
//     subforms(): DSLForm[] | null {
//         return [] as DSLForm[];
//     },
// };

// const helpers: HelperRegistry = {
//     DSLForm: DSLMethod,
//     DSLFormFields: DSLMethodFields,
//     DSLFormFieldModel: DSLMethodFieldModel,
//     DSLFormFieldModelAccess: DSLMethodFieldModelAccess,
//     DSLFormFieldModelDynamic: DSLMethodFieldModelDynamic,
//     DSLFormFieldModelIfDefaults: DSLMethodFieldModelIfDefaults,
//     DSLFormFieldModelSourceFilterDefaults:
//         DSLMethodFieldModelSourceFilterDefaults,
//     DSLFormFieldModelSubfieldsDefaults: DSLMethodFieldModelSubfieldsDefaults,
//     DSLFormFieldsDefaults: DSLMethodFieldsDefaults,
//     DSLFormFieldView: DSLMethodFieldView,
//     DSLFormFieldViewEmbed: DSLMethodFieldViewEmbed,
//     DSLFormFieldViewGrid: DSLMethodFieldViewGrid,
//     DSLFormModel: DSLMethodModel,
//     DSLFormModelAccess: DSLMethodModelAccess,
//     DSLFormModelPrefillDefaults: DSLMethodModelPrefillDefaults,
//     DSLFormModelSectionsDefaults: DSLMethodModelSectionsDefaults,
//     DSLFormView: DSLMethodView,
//     DSLFormViewBlock: DSLMethodViewBlock,
//     DSLFormViewGrid: DSLMethodViewGrid,
//     DSLFormViewGridAggregateDefaults: DSLMethodViewGridAggregateDefaults,
//     DSLFormViewGridStyleDefaults: DSLMethodViewGridStyleDefaults,
// };

// const DSLMethodFields = {
//     //CM:2025-05-24 - NEVER ADD ANY METHODS HERE
// };

// const DSLMethodFieldModel = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodFieldModelAccess = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodFieldModelDynamic = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodFieldModelIfDefaults = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodFieldModelSourceFilterDefaults = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodFieldModelSubfieldsDefaults = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodFieldsDefaults = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodFieldView = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodFieldViewEmbed = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodFieldViewGrid = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodModel = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodModelAccess = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodModelPrefillDefaults = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodModelSectionsDefaults = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodView = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodViewBlock = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodViewGrid = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodViewGridAggregateDefaults = {
//     getPath(): string {
//         return "";
//     },
// };

// const DSLMethodViewGridStyleDefaults = {
//     getPath(): string {
//         return "";
//     },
// };

// type DSLMethod = typeof DSLMethod;
// type DSLMethodFields = typeof DSLMethodFields;
// type DSLMethodFieldModel = typeof DSLMethodFieldModel;
// type DSLMethodFieldModelAccess = typeof DSLMethodFieldModelAccess;
// type DSLMethodFieldModelDynamic = typeof DSLMethodFieldModelDynamic;
// type DSLMethodFieldModelIfDefaults = typeof DSLMethodFieldModelIfDefaults;
// type DSLMethodFieldModelSourceFilterDefaults =
//     typeof DSLMethodFieldModelSourceFilterDefaults;
// type DSLMethodFieldModelSubfieldsDefaults =
//     typeof DSLMethodFieldModelSubfieldsDefaults;
// type DSLMethodFieldsDefaults = typeof DSLMethodFieldsDefaults;
// type DSLMethodFieldView = typeof DSLMethodFieldView;
// type DSLMethodFieldViewEmbed = typeof DSLMethodFieldViewEmbed;
// type DSLMethodFieldViewGrid = typeof DSLMethodFieldViewGrid;
// type DSLMethodModel = typeof DSLMethodModel;
// type DSLMethodModelAccess = typeof DSLMethodModelAccess;
// type DSLMethodModelPrefillDefaults = typeof DSLMethodModelPrefillDefaults;
// type DSLMethodModelSectionsDefaults = typeof DSLMethodModelSectionsDefaults;
// type DSLMethodView = typeof DSLMethodView;
// type DSLMethodViewBlock = typeof DSLMethodViewBlock;
// type DSLMethodViewGrid = typeof DSLMethodViewGrid;
// type DSLMethodViewGridAggregateDefaults =
//     typeof DSLMethodViewGridAggregateDefaults;
// type DSLMethodViewGridStyleDefaults = typeof DSLMethodViewGridStyleDefaults;

// type HelperRegistry = {
//     DSLForm: DSLMethod;
//     DSLFormFields: DSLMethodFields;
//     DSLFormFieldModel: typeof DSLMethodFieldModel;
//     DSLFormFieldModelAccess: typeof DSLMethodFieldModelAccess;
//     DSLFormFieldModelDynamic: typeof DSLMethodFieldModelDynamic;
//     DSLFormFieldModelIfDefaults: typeof DSLMethodFieldModelIfDefaults;
//     DSLFormFieldModelSourceFilterDefaults: typeof DSLMethodFieldModelSourceFilterDefaults;
//     DSLFormFieldModelSubfieldsDefaults: typeof DSLMethodFieldModelSubfieldsDefaults;
//     DSLFormFieldsDefaults: typeof DSLMethodFieldsDefaults;
//     DSLFormFieldView: typeof DSLMethodFieldView;
//     DSLFormFieldViewEmbed: typeof DSLMethodFieldViewEmbed;
//     DSLFormFieldViewGrid: typeof DSLMethodFieldViewGrid;
//     DSLFormModel: typeof DSLMethodModel;
//     DSLFormModelAccess: typeof DSLMethodModelAccess;
//     DSLFormModelPrefillDefaults: typeof DSLMethodModelPrefillDefaults;
//     DSLFormModelSectionsDefaults: typeof DSLMethodModelSectionsDefaults;
//     DSLFormView: typeof DSLMethodView;
//     DSLFormViewBlock: typeof DSLMethodViewBlock;
//     DSLFormViewGrid: typeof DSLMethodViewGrid;
//     DSLFormViewGridAggregateDefaults: typeof DSLMethodViewGridAggregateDefaults;
//     DSLFormViewGridStyleDefaults: typeof DSLMethodViewGridStyleDefaults;
// };
