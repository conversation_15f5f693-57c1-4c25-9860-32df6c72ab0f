// import { DSLForm } from "./form.ts";

// export const DSLMethod = {
//     load(id: string | number): string {
//         // ‘this’ is DSL<'Form'>
//         return String(id);
//     },
//     rows(): (typeof DSLForm)[] | null {
//         return [] as (typeof DSLForm)[];
//     },
//     subforms(): (typeof DSLForm)[] | null {
//         return [] as (typeof DSLForm)[];
//     },
// };

export const DSLMethodFields = {
    //CM:2025-05-24 - NEVER ADD ANY METHODS HERE
};

export const DSLMethodFieldModel = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodFieldModelAccess = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodFieldModelDynamic = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodFieldModelIfDefaults = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodFieldModelSourceFilterDefaults = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodFieldModelSubfieldsDefaults = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodFieldsDefaults = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodFieldView = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodFieldViewEmbed = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodFieldViewGrid = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodModel = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodModelAccess = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodModelPrefillDefaults = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodModelSectionsDefaults = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodView = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodViewBlock = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodViewGrid = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodViewGridAggregateDefaults = {
    getPath(): string {
        return "";
    },
};

export const DSLMethodViewGridStyleDefaults = {
    getPath(): string {
        return "";
    },
};
