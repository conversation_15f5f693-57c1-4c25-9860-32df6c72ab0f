/* eslint-disable @typescript-eslint/no-empty-object-type */
import { TypePrimitive, TypeStripThis } from "../../type/generics.ts";
import type * as Spec from "@clara/dsl/spec/req";

declare const ˆtype: unique symbol;

const DSLMethods = {
    DSLMethod: {
        load(id: string | number): string {
            return String(id);
        },
        rows(): DSLForm[] | null {
            return [] as DSLForm[];
        },
        subforms(): DSLForm[] | null {
            return [] as DSLForm[];
        },
    },
    DSLMethodFields: {
        //CM:2025-05-24 - CANNOT ADD ANY METHODS HERE
    },
    DSLMethodFieldModel: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodFieldModelAccess: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodFieldModelDynamic: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodFieldModelIfDefaults: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodFieldModelSourceFilterDefaults: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodFieldModelSubfieldsDefaults: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodFieldsDefaults: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodFieldView: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodFieldViewEmbed: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodFieldViewGrid: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodModel: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodModelAccess: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodModelPrefillDefaults: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodModelSectionsDefaults: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodView: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodViewBlock: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodViewGrid: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodViewGridAggregateDefaults: {
        getValue(): string {
            return "";
        },
    },

    DSLMethodViewGridStyleDefaults: {
        getValue(): string {
            return "";
        },
    },
} as const;

type DSLMethods = typeof DSLMethods;

type MethodKeyToFormKey<K extends string> = K extends `DSLMethod${infer Rest}`
    ? `DSLForm${Rest}`
    : never;

type HelperRegistry = {
    [K in keyof DSLMethods as MethodKeyToFormKey<K & string>]: DSLMethods[K];
};

type TagKey = keyof HelperRegistry;
type Branded<K extends TagKey> = { readonly [ˆtype]: K };
type BaseName = keyof SpecMapping;
type ToMethodKey<B extends BaseName> = B extends ""
    ? "DSLMethod"
    : `DSLMethod${B}`;
type ToFormKey<B extends BaseName> = B extends "" ? "DSLForm" : `DSLForm${B}`;

type makeDSL<BN extends BaseName> = SpecMapping[BN] &
    DSLMethods[ToMethodKey<BN> & keyof DSLMethods] &
    Branded<ToFormKey<BN> & TagKey>;

type DeepHelpers<
    T,
    ParentObjType = undefined,
    RootObjType = any, // This will be the type of the fully processed root object
> =
    // Primitives and functions are returned as is
    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
    [T] extends [TypePrimitive] | [Function]
        ? T
        : // For objects:
          T &
                TypeStripThis<HelperOf<T>> &
                (ParentObjType extends undefined
                    ? {} // No parent for the root
                    : { readonly ˆparent: ParentObjType }) & {
                    readonly ˆroot: RootObjType; // Points to the top-most processed object
                    readonly ˆpath: string; // Period-delimited path string from root
                    readonly ˆtype: TagKey;
                } extends infer SelfType
          ? // Result is SelfType, with its properties recursively typed by DeepHelpers.
            SelfType & {
                [K in keyof T]: DeepHelpers<
                    T[K],
                    SelfType, // Current object becomes parent for children
                    RootObjType // Root is passed down
                >;
            }
          : never;

type SpecMapping = {
    "": Spec.DSLReq;
    Fields: Spec.DSLReqFields;
    FieldModel: Spec.DSLReqFieldModel;
    FieldModelAccess: Spec.DSLReqFieldModelAccess;
    FieldModelDynamic: Spec.DSLReqFieldModelDynamic;
    FieldModelIfDefaults: Spec.DSLReqFieldModelIfDefaults;
    FieldModelSourceFilterDefaults: Spec.DSLReqFieldModelSourceFilterDefaults;
    FieldModelSubfieldsDefaults: Spec.DSLReqFieldModelSubfieldsDefaults;
    FieldsDefaults: Spec.DSLReqFieldsDefaults;
    FieldView: Spec.DSLReqFieldView;
    FieldViewEmbed: Spec.DSLReqFieldViewEmbed;
    FieldViewGrid: Spec.DSLReqFieldViewGrid;
    Model: Spec.DSLReqModel;
    ModelAccess: Spec.DSLReqModelAccess;
    ModelPrefillDefaults: Spec.DSLReqModelPrefillDefaults;
    ModelSectionsDefaults: Spec.DSLReqModelSectionsDefaults;
    View: Spec.DSLReqView;
    ViewBlock: Spec.DSLReqViewBlock;
    ViewGrid: Spec.DSLReqViewGrid;
    ViewGridAggregateDefaults: Spec.DSLReqViewGridAggregateDefaults;
    ViewGridStyleDefaults: Spec.DSLReqViewGridStyleDefaults;
};

// prettier-ignore
type HelperOf<T> =
  T extends Spec.DSLReq ? DSLForm
  : T extends Spec.DSLReqFields ? DSLFormFields
  : T extends Spec.DSLReqFieldsDefaults ?  DSLFormFieldsDefaults
  : T extends Spec.DSLReqFieldModel ?  DSLFormFieldModel
  : T extends Spec.DSLReqFieldModelAccess ?  DSLFormFieldModelAccess
  : T extends Spec.DSLReqFieldModelDynamic ?  DSLFormFieldModelDynamic
  : T extends Spec.DSLReqFieldModelIfDefaults ?  DSLFormFieldModelIfDefaults
  : T extends Spec.DSLReqFieldModelSourceFilterDefaults ?  DSLFormFieldModelSourceFilterDefaults
  : T extends Spec.DSLReqFieldModelSubfieldsDefaults ?  DSLFormFieldModelSubfieldsDefaults
  : T extends Spec.DSLReqFieldView ?  DSLFormFieldView
  : T extends Spec.DSLReqFieldViewEmbed ?  DSLFormFieldViewEmbed
  : T extends Spec.DSLReqFieldViewGrid ?  DSLFormFieldViewGrid
  : T extends Spec.DSLReqModel ?  DSLFormModel
  : T extends Spec.DSLReqModelAccess ?  DSLFormModelAccess
  : T extends Spec.DSLReqModelPrefillDefaults ?  DSLFormModelPrefillDefaults
  : T extends Spec.DSLReqModelSectionsDefaults ?  DSLFormModelSectionsDefaults
  : T extends Spec.DSLReqView ?  DSLFormView
  : T extends Spec.DSLReqViewBlock ?  DSLFormViewBlock
  : T extends Spec.DSLReqViewGrid ?  DSLFormViewGrid
  : T extends Spec.DSLReqViewGridAggregateDefaults ?  DSLFormViewGridAggregateDefaults
  : T extends Spec.DSLReqViewGridStyleDefaults ?  DSLFormViewGridStyleDefaults
  : {}; // fallback: nothing

export interface DSLForm extends makeDSL<""> {}
export interface DSLFormFields extends makeDSL<"Fields"> {}
export interface DSLFormFieldModel extends makeDSL<"FieldModel"> {}
export interface DSLFormFieldModelAccess extends makeDSL<"FieldModelAccess"> {}
export interface DSLFormFieldModelDynamic
    extends makeDSL<"FieldModelDynamic"> {}
export interface DSLFormFieldModelIfDefaults
    extends makeDSL<"FieldModelIfDefaults"> {}
export interface DSLFormFieldModelSourceFilterDefaults
    extends makeDSL<"FieldModelSourceFilterDefaults"> {}
export interface DSLFormFieldModelSubfieldsDefaults
    extends makeDSL<"FieldModelSubfieldsDefaults"> {}
export interface DSLFormFieldsDefaults extends makeDSL<"FieldsDefaults"> {}
export interface DSLFormFieldView extends makeDSL<"FieldView"> {}
export interface DSLFormFieldViewEmbed extends makeDSL<"FieldViewEmbed"> {}
export interface DSLFormFieldViewGrid extends makeDSL<"FieldViewGrid"> {}
export interface DSLFormModelAccess extends makeDSL<"ModelAccess"> {}
export interface DSLFormModel extends makeDSL<"Model"> {}
export interface DSLFormModelPrefillDefaults
    extends makeDSL<"ModelPrefillDefaults"> {}
export interface DSLFormModelSectionsDefaults
    extends makeDSL<"ModelSectionsDefaults"> {}
export interface DSLFormView extends makeDSL<"View"> {}
export interface DSLFormViewBlock extends makeDSL<"ViewBlock"> {}
export interface DSLFormViewGrid extends makeDSL<"ViewGrid"> {}
export interface DSLFormViewGridAggregateDefaults
    extends makeDSL<"ViewGridAggregateDefaults"> {}
export interface DSLFormViewGridStyleDefaults
    extends makeDSL<"ViewGridStyleDefaults"> {}

const helpers = Object.fromEntries(
    Object.entries(DSLMethods).map(([methodKey, methodImpl]) => [
        methodKey.replace(/^DSLMethod/, "DSLForm"),
        methodImpl,
    ])
) as HelperRegistry;

function attach(
    o: any,
    parentObject?: any,
    rootObjectParam?: any, // The actual root object instance being built
    parentPath?: string // Path of the parent object
): any {
    if (o === null || typeof o !== "object") return o;

    const ˆtype = "ˆtype";
    const tag = o[ˆtype] as TagKey | undefined;
    console.log(ˆtype, tag);
    const helperMethods = tag ? helpers[tag] : {};
    const instance = Object.assign(Object.create(helperMethods), o);

    const actualRoot = rootObjectParam || instance; // If rootObjectParam is undefined, this instance is the root.

    Object.defineProperty(instance, "ˆroot", {
        value: actualRoot,
        enumerable: false,
        writable: false,
        configurable: true,
    });

    Object.defineProperty(instance, "ˆpath", {
        value: parentPath,
        enumerable: false,
        writable: false,
        configurable: true,
    });

    if (parentObject) {
        Object.defineProperty(instance, "ˆparent", {
            value: parentObject,
            enumerable: false,
            writable: false,
            configurable: true,
        });
    }

    for (const key of Object.keys(o)) {
        if (Object.prototype.hasOwnProperty.call(o, key)) {
            const value = o[key];
            instance[key] = attach(
                value,
                instance,
                actualRoot,
                parentPath ? `${parentPath}.${key}` : key
            );
        }
    }
    return instance;
}

export function dslForm<T extends Spec.DSLReq>(data: T): DeepHelpers<T> {
    // Initial call to attach, no parent for the root object
    return attach(data) as DeepHelpers<T>;
}
