import type {
    <PERSON>LReq,
    <PERSON><PERSON>eqFields,
    DSLReqFieldsDefaults,
} from "@clara/dsl/spec/req";

// Test data matching your DSL spec
const testData: DSLReq = {
    fields: {
        patient_name: {
            model: {
                access: { read: [], if: null, write: [] },
                active: true,
                autoinsert: false,
                fk: false,
                default: "John Doe",
                if: {},
                ledger: null,
                max: null,
                min: null,
                multi: false,
                prefill: [],
                required: true,
                required_all: false,
                rounding: null,
                save: true,
                search: null,
                query: null,
                querytemplate: null,
                source: null,
                source_order: null,
                sourceid: null,
                track: false,
                sourcefilter: {},
                subfields: {},
                subfields_sort: [],
                template: null,
                transform: [],
                transform_filter: [],
                transform_post: [],
                type: "text",
                validate: [],
                dynamic: { source: null, type: null, query: null },
            },
            view: {
                form_link_enabled: false,
                class: null,
                embed: {
                    request_type: "GET",
                    add_form: null,
                    form: null,
                    query: null,
                    selectable: false,
                    add_preset: null,
                },
                add_preset: null,
                control: "input",
                columns: null,
                findfilter: null,
                findmulti: false,
                findunique: false,
                findrange: false,
                findwildcard: false,
                format: null,
                grid: {
                    add: null,
                    rank: null,
                    hide_cardmenu: false,
                    copy: [],
                    edit: false,
                    delete: false,
                    fields: [],
                    label: [],
                    split: false,
                    splitif: null,
                    deleteif: null,
                    text_trim: null,
                    tooltip: [],
                    width: [],
                    selectall: false,
                    allow_read_wo_id: false,
                    subfields: [],
                    subfields_label: [],
                    subfields_width: [],
                },
                requireall_bypass: false,
                requireif_bypass: false,
                highlight: null,
                label: "Patient Name",
                max_count: null,
                note: null,
                reference: null,
                offscreen: false,
                readonly: false,
                template: null,
                transform: [],
                validate: [],
                _meta: null,
            },
        },
        patient_email: {
            model: {
                access: { read: [], if: null, write: [] },
                active: true,
                autoinsert: false,
                fk: false,
                default: null,
                if: {},
                ledger: null,
                max: null,
                min: null,
                multi: false,
                prefill: [],
                required: false,
                required_all: false,
                rounding: null,
                save: true,
                search: null,
                query: null,
                querytemplate: null,
                source: null,
                source_order: null,
                sourceid: null,
                track: false,
                sourcefilter: {},
                subfields: {},
                subfields_sort: [],
                template: null,
                transform: [],
                transform_filter: [],
                transform_post: [],
                type: "text",
                validate: [],
                dynamic: { source: null, type: null, query: null },
            },
            view: {
                form_link_enabled: false,
                class: null,
                embed: {
                    request_type: "GET",
                    add_form: null,
                    form: null,
                    query: null,
                    selectable: false,
                    add_preset: null,
                },
                add_preset: null,
                control: "input",
                columns: null,
                findfilter: null,
                findmulti: false,
                findunique: false,
                findrange: false,
                findwildcard: false,
                format: null,
                grid: {
                    add: null,
                    rank: null,
                    hide_cardmenu: false,
                    copy: [],
                    edit: false,
                    delete: false,
                    fields: [],
                    label: [],
                    split: false,
                    splitif: null,
                    deleteif: null,
                    text_trim: null,
                    tooltip: [],
                    width: [],
                    selectall: false,
                    allow_read_wo_id: false,
                    subfields: [],
                    subfields_label: [],
                    subfields_width: [],
                },
                requireall_bypass: false,
                requireif_bypass: false,
                highlight: null,
                label: "Email Address",
                max_count: null,
                note: null,
                reference: null,
                offscreen: false,
                readonly: false,
                template: null,
                transform: [],
                validate: [],
                _meta: null,
            },
        },
    },
    model: {
        access: {
            create: [],
            create_all: [],
            read: [],
            read_all: [],
            update: [],
            update_all: [],
            delete: [],
            request: [],
            review: [],
            rereview: [],
            write: [],
        },
        bundle: [],
        collections: [],
        indexes: { gin: [], fulltext: [], lower: [], many: [], unique: [] },
        large: false,
        ledger: null,
        name: "Patient Form",
        prefill: {},
        reportable: false,
        required_if: null,
        save: true,
        sections_group: [],
        sections: {},
        sections_order: null,
        sync_mode: null,
        transform: [],
        transform_filter: [],
        transform_post: [],
        validate: [],
    },
    view: {
        block: {
            print: { if: null, except: [] },
            update: { if: null, except: [] },
            validate: [],
        },
        comment: null,
        dimensions: null,
        find: { advanced: [], basic: [] },
        grid: {
            fields: [],
            width: [],
            sort: [],
            label: [],
            pivot: null,
            aggregate: {},
            group_by: null,
            style: {},
            menu: [],
            hide_columns: [],
        },
        hide_header: false,
        hide_cardmenu: false,
        icon: null,
        label: "Patient Information",
        max_rows: null,
        open: null,
        transform: [],
        validate: [],
        reference: null,
    },
};

const METHOD_SYMBOL = Symbol("dsl-methods");

// Method interfaces for IntelliSense
interface FieldMethods {
    [METHOD_SYMBOL]: "field";
    getValue(): any;
    setValue(value: any): void;
    isRequired(): boolean;
    getLabel(): string;
}

interface FieldsMethods {
    [METHOD_SYMBOL]: "fields";
    getField(name: string): DSLReqFieldsDefaults & FieldMethods;
    getAllValues(): Record<string, any>;
}

interface RootMethods {
    [METHOD_SYMBOL]: "root";
    getModelName(): string;
}

// Enhanced types for IntelliSense
type EnhancedField = DSLReqFieldsDefaults & FieldMethods;
type EnhancedFields = DSLReqFields &
    FieldsMethods & {
        [K in keyof DSLReqFields]: EnhancedField;
    };
type EnhancedDSLReq = DSLReq &
    RootMethods & {
        fields: EnhancedFields;
    };

// Runtime storage
const fieldValues = new WeakMap<object, Record<string, any>>();

// Properly typed proxy function
function createDeepProxy(obj: DSLReq): EnhancedDSLReq;
function createDeepProxy(
    obj: DSLReqFields,
    path: ["fields"],
    rootRef: object
): EnhancedFields;
function createDeepProxy(
    obj: DSLReqFieldsDefaults,
    path: ["fields", string],
    rootRef: object
): EnhancedField;
function createDeepProxy(obj: any, path: string[], rootRef: object): any;

function createDeepProxy(obj: any, path: string[] = [], rootRef?: object): any {
    const root = rootRef || obj;

    return new Proxy(obj, {
        get(target: any, prop) {
            const currentPath = [...path, String(prop)];

            // Method injection based on path pattern
            if (prop === METHOD_SYMBOL) {
                if (path.length === 0) return "root";
                if (path.length === 1 && path[0] === "fields") return "fields";
                if (path.length === 2 && path[0] === "fields") return "field";
                return undefined;
            }

            // Root methods (path: [])
            if (path.length === 0 && prop === "getModelName") {
                return () => target.model?.name || "Unknown";
            }

            // Fields methods (path: ['fields'])
            if (path.length === 1 && path[0] === "fields") {
                if (prop === "getField") {
                    return (name: string) =>
                        createDeepProxy(target[name], ["fields", name], root);
                }
                if (prop === "getAllValues") {
                    return () => fieldValues.get(root) || {};
                }
            }

            // Field methods (path: ['fields', fieldName])
            if (path.length === 2 && path[0] === "fields") {
                const fieldName = path[1];
                if (prop === "getValue") {
                    return () => {
                        const values = fieldValues.get(root) || {};
                        return values[fieldName] || target.model?.default;
                    };
                }
                if (prop === "setValue") {
                    return (value: any) => {
                        if (!fieldValues.has(root)) fieldValues.set(root, {});
                        fieldValues.get(root)![fieldName] = value;
                    };
                }
                if (prop === "isRequired") {
                    return () => target.model?.required || false;
                }
                if (prop === "getLabel") {
                    return () => target.view?.label || fieldName;
                }
            }

            const value = target[prop];

            // Continue proxying for objects with proper typing
            if (value && typeof value === "object" && !Array.isArray(value)) {
                if (prop === "fields") {
                    return createDeepProxy(value, ["fields"], root);
                }
                if (path.length === 1 && path[0] === "fields") {
                    return createDeepProxy(
                        value,
                        ["fields", String(prop)],
                        root
                    );
                }
                return createDeepProxy(value, currentPath, root);
            }

            return value;
        },
    });
}

// Test usage with full IntelliSense
const enhanced = createDeepProxy(testData);

// Now you get full IntelliSense:
enhanced.getModelName(); // ✅ IntelliSense works
enhanced.fields.getAllValues(); // ✅ IntelliSense works
enhanced.fields.patient_name.getValue(); // ✅ IntelliSense works
console.log(enhanced.fields.patient_name.model.type); // ✅ IntelliSense works
console.log(enhanced.fields.patient_name.view.label); // ✅ IntelliSense works

export { enhanced, createDeepProxy, type EnhancedDSLReq };

// import DSLListUSState from "@clara/dsl/list_us_state";

// const t = DSLListUSState;

// // console.log(t.fields.code.view.embed.ˆroot.fields.ˆpath);
// // console.log(t.model.sections.ˆpath);
// // console.log(t.fields.name.ˆpath);
// console.log(t.fields.auto_name.ˆtype);

// ˆ

// import { deepMerge } from "../utils/deep.ts";
// import type { DSLSpecRequired } from "@clara/dsl";

// function DSLForm(dsl: any) {
//     return deepMerge(dsl, {});
// }

// interface DSLForm extends DSLSpecRequired {
//     form: string;
// }

// Yes. Just add it as a method (or optional method) on the interface:

// type LoadResult = Awaited<ReturnType<typeof someLoader>>; // whatever fits

// export default interface DSLOpt {
//   fields: DSLOptFields;
//   model: DSLOptModel;
//   view: DSLOptView;

//   /**
//    * Sync
//    */
//   // load(): void;

//   /**
//    * Async (typical)
//    */
//   load?: () => Promise<LoadResult>;
// }

// Usage:

// const opts: DSLOpt = {
//   fields,
//   model,
//   view,
//   async load() {
//     return await someLoader();
//   },
// };

// await opts.load?.();

// interface MyType {
//     readonly id: number;
//     readonly value: string;
// }

// const myConstObj: MyType = { id: 1, value: "test" };

// // Define methods, 'this' will refer to the new composite object.
// // Explicit 'this' typing for clarity and safety, especially if methods call each other.
// type ExtendedMyType = MyType & {
//     getValueUpper: () => string;
//     describe: (prefix: string) => string;
// };

// const methods = {
//     getValueUpper(this: MyType): string {
//         return this.value.toUpperCase();
//     },
//     describe(this: ExtendedMyType, prefix: string): string {
//         // 'this' can be the extended type
//         return `${prefix}: ${this.getValueUpper()} (ID: ${this.id})`;
//     },
// };

// const extendedObj: ExtendedMyType = {
//     ...myConstObj,
//     ...methods, // Or define methods inline:
//     // getValueUpper() { return this.value.toUpperCase(); },
//     // describe(prefix: string) { return `${prefix}: ${this.getValueUpper()} (ID: ${this.id})`; }
// };

// console.log(extendedObj.id); // 1
// console.log(extendedObj.getValueUpper()); // TEST
// console.log(extendedObj.describe("Object")); // Object: TEST (ID: 1)

// myConstObj remains unchanged and without new methods.
// (myConstObj as any).getValueUpper(); // Runtime error / TS error
