/* eslint-disable @typescript-eslint/no-empty-object-type */

export interface DSLForm {}
export interface DSLFormFieldModel {}
export interface DSLFormFieldModelAccess {}
export interface DSLFormFieldModelDynamic {}
export interface DSLFormFieldModelIfDefaults {}
export interface DSLFormFieldModelSourceFilterDefaults {}
export interface DSLFormFieldModelSubfieldsDefaults {}
export interface DSLFormFields {}
export interface DSLFormFieldsDefaults {}
export interface DSLFormFieldView {}
export interface DSLFormFieldViewEmbed {}
export interface DSLFormFieldViewGrid {}
export interface DSLFormModel {}
export interface DSLFormModelAccess {}
export interface DSLFormModelPrefillDefaults {}
export interface DSLFormModelSectionsDefaults {}
export interface DSLFormView {}
export interface DSLFormViewBlock {}
export interface DSLFormViewGrid {}
export interface DSLFormViewGridAggregateDefaults {}
export interface DSLFormViewGridStyleDefaults {}

export type DSLRegistry = {
    DSLReq: DSLForm;
    DSLReqFieldModel: DSLFormFieldModel;
    DSLReqFieldModelAccess: DSLFormFieldModelAccess;
    DSLReqFieldModelDynamic: DSLFormFieldModelDynamic;
    DSLReqFieldModelIfDefaults: DSLFormFieldModelIfDefaults;
    DSLReqFieldModelSourceFilterDefaults: DSLFormFieldModelSourceFilterDefaults;
    DSLReqFieldModelSubfieldsDefaults: DSLFormFieldModelSubfieldsDefaults;
    DSLReqFields: DSLFormFields;
    DSLReqFieldsDefaults: DSLFormFieldsDefaults;
    DSLReqFieldView: DSLFormFieldView;
    DSLReqFieldViewEmbed: DSLFormFieldViewEmbed;
    DSLReqFieldViewGrid: DSLFormFieldViewGrid;
    DSLReqModel: DSLFormModel;
    DSLReqModelAccess: DSLFormModelAccess;
    DSLReqModelPrefillDefaults: DSLFormModelPrefillDefaults;
    DSLReqModelSectionsDefaults: DSLFormModelSectionsDefaults;
    DSLReqView: DSLFormView;
    DSLReqViewBlock: DSLFormViewBlock;
    DSLReqViewGrid: DSLFormViewGrid;
    DSLReqViewGridAggregateDefaults: DSLFormViewGridAggregateDefaults;
    DSLReqViewGridStyleDefaults: DSLFormViewGridStyleDefaults;
};
