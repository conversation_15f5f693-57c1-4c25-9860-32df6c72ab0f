/* eslint-disable @typescript-eslint/no-empty-object-type */

// Model Interfaces
export interface DSLReqFieldModelAccess {
    read: any[];
    if: string | null;
    write: any[];
}

export interface DSLReqFieldModelIfDefaults {
    fields: any[];
    note: string | null;
    sections: any[];
    require_fields: any[];
    hide_fields: any[];
    hide_sections: any[];
    require_any: any[];
    field_warning: {} | null;
    highlight: string | null;
    trigger: any[];
    readonly: {
        fields: any[];
        sections: any[];
    };
    prefill: {} | null;
    form_label: string | null;
    field_label: {} | null;
}

export interface DSLReqFieldModelSourceFilterDefaults {
    dynamic: number | string | null;
    static: number | string | any[] | null;
    source: any[] | null;
    default: number | string | null;
}

export interface DSLReqFieldModelSubfieldsDefaults {
    label: string | null;
    style: {} | null;
    readonly: boolean;
    required: boolean;
    multi: boolean;
    offscreen: boolean;
    source: string | any[] | {} | null;
    dynamic: string | null;
    format:
        | ""
        | "0,0.[000000]"
        | "$0,0.[000000]"
        | "$0,0.00"
        | "$0,0.0000"
        | "0,0"
        | "percent"
        | "us_phone"
        | null;
    sourceid: string | null;
    sourcefilter: {
        [x: string]: DSLReqFieldModelSourceFilterDefaults;
    };
    type:
        | ""
        | "date"
        | "datenow"
        | "datetime"
        | "decimal"
        | "int"
        | "text"
        | "time"
        | "timenow"
        | "timestamp"
        | "checkbox"
        | "area"
        | null;
    class: string | null;
}

export interface DSLReqFieldModelDynamic {
    source: string | null;
    type: "" | "text" | null;
    query: string | null;
}

export interface DSLReqFieldModel {
    access: DSLReqFieldModelAccess;
    active: boolean;
    autoinsert: boolean;
    fk: boolean;
    default: any;
    if: {
        [x: string]: DSLReqFieldModelIfDefaults;
    };
    ledger: string | null;
    max: number | string | null;
    min: number | string | null;
    multi: boolean;
    prefill: any[];
    required: boolean;
    required_all: boolean;
    rounding: number | null;
    save: boolean;
    search: string | null;
    query: string | null;
    querytemplate: string | null;
    source: string | any[] | {} | null;
    source_order: any[] | null;
    sourceid: string | null;
    track: boolean;
    sourcefilter: {
        [x: string]: DSLReqFieldModelSourceFilterDefaults;
    };
    subfields: {
        [x: string]: DSLReqFieldModelSubfieldsDefaults;
    };
    subfields_sort: any[];
    template: string | null;
    transform: any[];
    transform_filter: any[];
    transform_post: any[];
    type:
        | ""
        | "date"
        | "datetime"
        | "decimal"
        | "image"
        | "int"
        | "json"
        | "xml"
        | "password"
        | "subform"
        | "text"
        | "time"
        | "color"
        | null;
    validate: any[];
    dynamic: DSLReqFieldModelDynamic;
}

// View Interfaces
export interface DSLReqFieldViewEmbed {
    request_type: "GET" | "POST";
    add_form: string | null;
    form: string | null;
    query: string | null;
    selectable: boolean;
    add_preset: {} | null;
}

export interface DSLReqFieldViewGrid {
    add: "" | "flyout" | "inline" | "none" | null;
    rank: "" | "none" | "local" | "global" | null;
    hide_cardmenu: boolean;
    copy: any[];
    edit: boolean;
    delete: boolean;
    fields: any[];
    label: any[];
    split: boolean;
    splitif: {} | null;
    deleteif: {} | null;
    text_trim: number | null;
    tooltip: any[];
    width: any[];
    selectall: boolean;
    allow_read_wo_id: boolean;
    subfields: any[];
    subfields_label: any[];
    subfields_width: any[];
}

export interface DSLReqFieldView {
    form_link_enabled: boolean;
    class: string | null;
    embed: DSLReqFieldViewEmbed;
    add_preset: {} | null;
    control:
        | ""
        | "area"
        | "barcode"
        | "checkbox"
        | "esign"
        | "file"
        | "grid"
        | "inline"
        | "input"
        | "link"
        | "radio"
        | "paycard"
        | "picker"
        | "select"
        | "subform"
        | "xml"
        | "embedded_table"
        | "raw"
        | "json"
        | null;
    columns: number | string | null;
    findfilter: string | any[] | null;
    findmulti: boolean;
    findunique: boolean;
    findrange: boolean;
    findwildcard: boolean;
    format:
        | ""
        | "hic"
        | "ssn"
        | "us_phone"
        | "us_zip"
        | "url"
        | "0,0.[000000]"
        | "$0,0.[000000]"
        | "$0,0.00"
        | "$0,0.0000"
        | "0,0"
        | "percent"
        | null;
    grid: DSLReqFieldViewGrid;
    requireall_bypass: boolean;
    requireif_bypass: boolean;
    highlight: string | null;
    label: string | null;
    max_count: number | null;
    note: string | null;
    reference: string | any[] | null;
    offscreen: boolean;
    readonly: boolean;
    template: string | null;
    transform: any[];
    validate: any[];
    _meta: string | any[] | {} | null;
}

export interface DSLReqFieldsDefaults {
    model: DSLReqFieldModel;
    view: DSLReqFieldView;
}

// Root Interfaces
export interface DSLReqFields {
    [x: string]: DSLReqFieldsDefaults;
}

export interface DSLReqModelAccess {
    create: any[];
    create_all: any[];
    read: any[];
    read_all: any[];
    update: any[];
    update_all: any[];
    delete: any[];
    request: any[];
    review: any[];
    rereview: any[];
    write: any[];
}

export interface DSLReqModelPrefillDefaults {
    filter: {} | null;
    link: {} | null;
    max: string | null;
    min: string | null;
}

export interface DSLReqModelSectionsDefaults {
    fields: any[];
    tab: string | null;
    tab_toggle: boolean;
    hide_header: boolean;
    indent: boolean;
    compact: boolean;
    group: {} | null;
    note: string | null;
    area: string | null;
    prefill: string | null;
    modal: boolean;
}

export interface DSLReqModel {
    access: DSLReqModelAccess;
    bundle: any[];
    collections: any[];
    indexes: {
        gin: any[];
        fulltext: any[];
        lower: any[];
        many: any[];
        unique: any[];
    };
    large: boolean;
    ledger: string | null;
    name: string | any[] | null;
    prefill: {
        [x: string]: DSLReqModelPrefillDefaults;
    };
    reportable: boolean;
    required_if: string | null;
    save: boolean;
    sections_group: any[];
    sections: {
        [x: string]: DSLReqModelSectionsDefaults;
    };
    sections_order: any[] | null;
    sync_mode: "" | "full" | "mixed" | "none" | null;
    transform: any[];
    transform_filter: any[];
    transform_post: any[];
    validate: any[];
}

export interface DSLReqViewBlock {
    if: string | null;
    except: any[];
}

export interface DSLReqViewGridAggregateDefaults {
    field: string | null;
    func: string | null;
}

export interface DSLReqViewGridStyleDefaults {
    style: {} | null;
}

export interface DSLReqViewGrid {
    fields: any[];
    width: any[];
    sort: any[];
    label: any[];
    pivot: string | null;
    aggregate: {
        [x: string]: DSLReqViewGridAggregateDefaults;
    };
    group_by: string | null;
    style: {
        [x: string]: DSLReqViewGridStyleDefaults;
    };
    menu: any[];
    hide_columns: any[];
}

export interface DSLReqView {
    block: {
        print: DSLReqViewBlock;
        update: DSLReqViewBlock;
        validate: any[];
    };
    comment: string | null;
    dimensions: {} | null;
    find: {
        advanced: any[];
        basic: any[];
    };
    grid: DSLReqViewGrid;
    hide_header: boolean;
    hide_cardmenu: boolean;
    icon: string | null;
    label: string | null;
    max_rows: number | null;
    open: "" | "read" | "edit" | null;
    transform: any[];
    validate: any[];
    reference: string | any[] | null;
}

// Root DSLReq Interface
export interface DSLReq {
    fields: DSLReqFields;
    model: DSLReqModel;
    view: DSLReqView;
}

export default DSLReq;
